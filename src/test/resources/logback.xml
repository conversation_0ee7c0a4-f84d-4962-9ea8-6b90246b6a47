<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Test environment logback configuration -->
    
    <!-- Console appender for test output -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%X{tid}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- File appender for test logs -->
    <appender name="TEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./target/test-logs/test.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./target/test-logs/test-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} [%X{tid}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- Async appender for better performance -->
    <appender name="ASYNC_TEST_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="TEST_FILE"/>
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>
    
    <!-- Logger configurations -->
    
    <!-- Your application package - set to DEBUG for detailed test logging -->
    <logger name="com.cloudpod.podsail" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </logger>
    
    <!-- CloudPods client libraries - DEBUG level to see internal operations -->
    <logger name="com.yunionyun.mcp" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </logger>

    <!-- Specific ServerManager and managers debug logging -->
    <logger name="com.yunionyun.mcp.mcclient.managers" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </logger>

    <!-- ServerManager specific logging -->
    <logger name="com.yunionyun.mcp.mcclient.managers.impl.compute.ServerManager" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </logger>

    <!-- Base manager classes -->
    <logger name="com.yunionyun.mcp.mcclient.managers.impl" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </logger>
    
    <!-- HTTP client logging - DEBUG level for detailed API call tracing -->
    <logger name="org.apache.http" level="DEBUG"/>
    <logger name="httpclient.wire" level="DEBUG"/>
    <logger name="org.apache.http.client" level="DEBUG"/>
    <logger name="org.apache.http.impl" level="DEBUG"/>
    <logger name="org.apache.http.headers" level="DEBUG"/>

    <!-- Authentication and session management -->
    <logger name="com.yunionyun.mcp.mcclient.keystone" level="DEBUG"/>
    <logger name="com.yunionyun.mcp.mcclient.AuthAgent" level="DEBUG"/>
    <logger name="com.yunionyun.mcp.mcclient.Session" level="DEBUG"/>
    <logger name="com.yunionyun.mcp.mcclient.Client" level="DEBUG"/>

    <!-- JSON processing and request/response handling -->
    <logger name="com.yunionyun.mcp.mcclient.common" level="DEBUG"/>
    <logger name="com.yunionyun.mcp.mcclient.utils" level="DEBUG"/>
    
    <!-- Spring framework - WARN level to reduce noise in tests -->
    <logger name="org.springframework" level="WARN"/>
    
    <!-- JUnit and test framework logging -->
    <logger name="org.junit" level="INFO"/>
    
    <!-- JSON processing -->
    <logger name="com.alibaba.fastjson" level="WARN"/>
    <logger name="org.json" level="WARN"/>
    
    <!-- Catch-all loggers for any missed packages -->
    <logger name="com.yunionyun" level="TRACE"/>
    <logger name="yunion" level="TRACE"/>
    <logger name="mcp" level="TRACE"/>

    <!-- Enable all possible HTTP and network logging -->
    <logger name="org.apache.http.wire" level="DEBUG"/>
    <logger name="org.apache.http.headers" level="DEBUG"/>
    <logger name="org.apache.http.impl.conn" level="DEBUG"/>
    <logger name="org.apache.http.impl.client" level="DEBUG"/>
    <logger name="org.apache.http.client.protocol" level="DEBUG"/>

    <!-- Root logger configuration - TRACE level for maximum logging -->
    <root level="TRACE">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </root>
    
</configuration>
