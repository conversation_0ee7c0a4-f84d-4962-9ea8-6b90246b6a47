<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Test environment logback configuration -->
    
    <!-- Console appender for test output -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%X{tid}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- File appender for test logs -->
    <appender name="TEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./target/test-logs/test.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>./target/test-logs/test-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} [%X{tid}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- Async appender for better performance -->
    <appender name="ASYNC_TEST_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="TEST_FILE"/>
        <queueSize>512</queueSize>
        <discardingThreshold>0</discardingThreshold>
    </appender>
    
    <!-- Logger configurations -->
    
    <!-- Your application package - set to DEBUG for detailed test logging -->
    <logger name="com.cloudpod.podsail" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </logger>
    
    <!-- CloudPods client libraries - INFO level to avoid too much noise -->
    <logger name="com.yunionyun.mcp" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_TEST_FILE"/>
    </logger>
    
    <!-- HTTP client logging - useful for debugging API calls -->
    <logger name="org.apache.http" level="INFO"/>
    <logger name="httpclient.wire" level="INFO"/>
    
    <!-- Spring framework - WARN level to reduce noise in tests -->
    <logger name="org.springframework" level="WARN"/>
    
    <!-- JUnit and test framework logging -->
    <logger name="org.junit" level="INFO"/>
    
    <!-- JSON processing -->
    <logger name="com.alibaba.fastjson" level="WARN"/>
    <logger name="org.json" level="WARN"/>
    
    <!-- Root logger configuration -->
    <root level="DEBUG">
        <appender-ref ref="CONSOLE"/>
    </root>
    
</configuration>
